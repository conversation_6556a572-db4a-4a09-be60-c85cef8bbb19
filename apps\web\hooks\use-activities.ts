import { useQuery } from "@tanstack/react-query";

export interface Activity {
  title: string;
  description: string;
  "senpai-count": number;
  hot: boolean;
  fav: boolean;
  tags: string[];
  cover: string;
  icon: string;
}

// Mock function to simulate API call for activities
const fetchActivities = async (): Promise<Activity[]> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  return activitiesData.default as Activity[];
};

// Enhanced fetch function with filtering and pagination
interface FetchActivitiesOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  page?: number;
  itemsPerPage?: number;
}

interface FetchActivitiesResult {
  activities: Activity[];
  totalItems: number;
  totalPages: number;
}

const fetchActivitiesFiltered = async (
  options: FetchActivitiesOptions = {},
): Promise<FetchActivitiesResult> => {
  const {
    searchValue = "",
    showFavorites = false,
    categoryFilters = {},
    page = 1,
    itemsPerPage = 15,
  } = options;

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 4000));

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  const allActivities = activitiesData.default as Activity[];

  // Filter activities based on the provided options
  const filteredActivities = allActivities.filter((activity) => {
    // Search filter
    if (
      searchValue &&
      !activity.title.toLowerCase().includes(searchValue.toLowerCase())
    ) {
      return false;
    }

    // Favorites filter
    if (showFavorites && !activity.fav) {
      return false;
    }

    // Category filters (placeholder - would need actual category mapping)
    const hasActiveFilters = Object.values(categoryFilters).some(Boolean);
    if (hasActiveFilters) {
      // This would need to be implemented based on how activities are categorized
      // For now, we'll just return true
      return true;
    }

    return true;
  });

  // Pagination
  const totalItems = filteredActivities.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedActivities = filteredActivities.slice(startIndex, endIndex);

  return {
    activities: paginatedActivities,
    totalItems,
    totalPages,
  };
};

export const useActivities = () => {
  return useQuery({
    queryKey: ["activities"],
    queryFn: fetchActivities,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for filtered activities with search, favorites, and pagination
export interface UseActivitiesFiltersOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  page?: number;
  itemsPerPage?: number;
}

export const useActivitiesFiltered = (
  options: UseActivitiesFiltersOptions = {},
) => {
  const { page = 1 } = options;

  const queryResult = useQuery({
    queryKey: ["activities", "filtered", options],
    queryFn: () => fetchActivitiesFiltered(options),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data } = queryResult;

  return {
    ...queryResult,
    data: data?.activities,
    totalItems: data?.totalItems || 0,
    totalPages: data?.totalPages || 0,
    currentPage: page,
    hasNextPage: page < (data?.totalPages || 0),
    hasPreviousPage: page > 1,
  };
};

// Function to generate page numbers for pagination
const generatePageNumbers = (
  currentPage: number,
  totalPages: number,
  maxVisiblePages: number = 5,
): (number | "ellipsis")[] => {
  const pages: (number | "ellipsis")[] = [];

  if (totalPages <= maxVisiblePages) {
    // Show all pages if total is small
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Show first page
    pages.push(1);

    if (currentPage > 3) {
      pages.push("ellipsis");
    }

    // Show pages around current page
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    if (currentPage < totalPages - 2) {
      pages.push("ellipsis");
    }

    // Show last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
  }

  return pages;
};

// Hook for getting page numbers with React Query caching
interface UsePageNumbersOptions {
  currentPage: number;
  totalPages: number;
  maxVisiblePages?: number;
}

export const usePageNumbers = (options: UsePageNumbersOptions) => {
  const { currentPage, totalPages, maxVisiblePages = 5 } = options;

  return useQuery({
    queryKey: ["pageNumbers", currentPage, totalPages, maxVisiblePages],
    queryFn: () =>
      generatePageNumbers(currentPage, totalPages, maxVisiblePages),
    staleTime: 10 * 60 * 1000, // 10 minutes - page numbers don't change often for same params
    enabled: totalPages > 0, // Only run when we have valid total pages
  });
};
