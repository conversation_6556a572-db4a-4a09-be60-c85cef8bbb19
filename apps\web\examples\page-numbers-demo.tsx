"use client";

import { useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { usePageNumbers } from "../hooks/use-activities";

// Create a query client for the demo
const queryClient = new QueryClient();

function PageNumbersDemo() {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(10);

  const { data: pageNumbers = [], isLoading, error } = usePageNumbers({
    currentPage,
    totalPages,
    maxVisiblePages: 5,
  });

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Page Numbers Hook Demo</h1>
      
      <div className="space-y-4 mb-8">
        <div>
          <label className="block text-sm font-medium mb-2">
            Current Page: {currentPage}
          </label>
          <input
            type="range"
            min="1"
            max={totalPages}
            value={currentPage}
            onChange={(e) => setCurrentPage(Number(e.target.value))}
            className="w-full"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">
            Total Pages: {totalPages}
          </label>
          <input
            type="range"
            min="1"
            max="20"
            value={totalPages}
            onChange={(e) => setTotalPages(Number(e.target.value))}
            className="w-full"
          />
        </div>
      </div>

      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Generated Page Numbers:</h2>
        
        {isLoading && <p>Loading...</p>}
        {error && <p className="text-red-500">Error: {String(error)}</p>}
        
        {pageNumbers.length > 0 && (
          <div className="flex gap-2 flex-wrap">
            {pageNumbers.map((page, index) => (
              <span
                key={index}
                className={`px-3 py-1 rounded ${
                  page === "ellipsis"
                    ? "bg-gray-300 text-gray-600"
                    : page === currentPage
                    ? "bg-blue-500 text-white"
                    : "bg-white border border-gray-300"
                }`}
              >
                {page === "ellipsis" ? "..." : page}
              </span>
            ))}
          </div>
        )}
        
        <div className="mt-4 text-sm text-gray-600">
          <p>Query Key: ["pageNumbers", {currentPage}, {totalPages}, 5]</p>
          <p>This demonstrates React Query caching for page number generation.</p>
        </div>
      </div>

      <div className="mt-8 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">How it works:</h3>
        <ul className="text-sm space-y-1">
          <li>• The hook uses React Query to cache page number calculations</li>
          <li>• Page numbers are generated based on current page, total pages, and max visible pages</li>
          <li>• Results are cached for 10 minutes to avoid recalculation</li>
          <li>• The query is disabled when totalPages is 0</li>
          <li>• Ellipsis ("...") is used to indicate skipped pages</li>
        </ul>
      </div>
    </div>
  );
}

export default function PageNumbersDemoPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <PageNumbersDemo />
    </QueryClientProvider>
  );
}
